import axiosInstance from '../config/axios';

export async function getStudentProfileData(forceRefresh = false) {
  const url = forceRefresh
    ? `/student-profile/all-data?_t=${Date.now()}`
    : '/student-profile/all-data';

  const response = await axiosInstance.get(url, {
    headers: {
      'Cache-Control': forceRefresh ? 'no-cache, no-store, must-revalidate' : 'default',
      'Pragma': forceRefresh ? 'no-cache' : 'default',
      'Expires': forceRefresh ? '0' : 'default',
    }
  });
  return response.data;
}

export async function updateStudentProfileData(data: any) {
  const response = await axiosInstance.put('/student-profile/combined', data);
  return response.data;
}

export async function getClassroomOptions() {
  const response = await axiosInstance.get('/student-profile/classroom-options');
  return response.data;
}

export async function getConstantsByCategory(category: string, forceRefresh = false) {
  const url = forceRefresh
    ? `/constant/${category}?_t=${Date.now()}`
    : `/constant/${category}`;

  const response = await axiosInstance.get(url, {
    headers: {
      'Cache-Control': forceRefresh ? 'no-cache, no-store, must-revalidate' : 'default',
      'Pragma': forceRefresh ? 'no-cache' : 'default',
      'Expires': forceRefresh ? '0' : 'default',
    }
  });
  return response.data;
}