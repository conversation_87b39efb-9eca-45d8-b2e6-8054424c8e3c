import axiosInstance from '../config/axios';

export async function studentRegister(data: any) {
  const response = await axiosInstance.post('/student/register', data);
  return response.data;
}

export async function googleLogin(data: any) {
  const response = await axiosInstance.post('/student/google-auth', data);
  return response.data;
}

export async function getCurrentStudent(forceRefresh = false) {
  const url = forceRefresh
    ? `/student/me?_t=${Date.now()}`
    : '/student/me';

  const response = await axiosInstance.get(url, {
    headers: {
      'Cache-Control': forceRefresh ? 'no-cache, no-store, must-revalidate' : 'default',
      'Pragma': forceRefresh ? 'no-cache' : 'default',
      'Expires': forceRefresh ? '0' : 'default',
    }
  });
  return response.data;
}